// Music Player JavaScript

// DOM Elements
const audioPlayer = document.getElementById('audio-player');
const playPauseButton = document.getElementById('play-pause-button');
const prevButton = document.getElementById('prev-button');
const nextButton = document.getElementById('next-button');
const volumeButton = document.getElementById('volume-button');
const volumeSlider = document.getElementById('volume-slider');
const lyricsButton = document.getElementById('lyrics-button');
const closeLyricsButton = document.getElementById('close-lyrics-button');
const lyricsContainer = document.getElementById('lyrics-container');
const lyricsContent = document.getElementById('lyrics-content');
const albumArt = document.getElementById('album-art');
const trackTitle = document.getElementById('track-title');
const trackArtist = document.getElementById('track-artist');
const trackAlbum = document.getElementById('track-album');
const trackYear = document.getElementById('track-year');
const trackGenre = document.getElementById('track-genre');
const currentTimeDisplay = document.getElementById('current-time');
const durationDisplay = document.getElementById('duration');
const progressBar = document.querySelector('.progress-bar');
const progress = document.querySelector('.progress');
const playlistElement = document.getElementById('playlist');
const playlistTitle = document.getElementById('playlist-title');
const playlistDescription = document.getElementById('playlist-description');
const searchInput = document.getElementById('search-input');
const searchButton = document.getElementById('search-button');
const albumArtContainer = document.querySelector('.album-art-container');
const playlistDropdown = document.getElementById('playlist-dropdown');

// Player State
let availablePlaylists = [];
let currentPlaylistId = null;
let playlist = [];
let currentTrackIndex = 0;
let isPlaying = false;
let isMuted = false;
let previousVolume = 1;

// Initialize the player
async function initPlayer() {
    try {
        // Load available playlists
        await loadPlaylistsConfig();

        // Set up event listeners
        setupEventListeners();

        // Load default playlist
        const defaultPlaylist = availablePlaylists.find(p => p.isDefault) || availablePlaylists[0];
        if (defaultPlaylist) {
            await loadPlaylist(defaultPlaylist.id);
        }

    } catch (error) {
        console.error('Error initializing player:', error);
        playlistTitle.textContent = 'Error loading playlists';
        playlistDescription.textContent = 'Please try again later';
    }
}

// Load playlists configuration
async function loadPlaylistsConfig() {
    try {
        const response = await fetch('data/playlists.json');
        const data = await response.json();
        availablePlaylists = data.playlists;

        // Populate dropdown
        populatePlaylistDropdown();

    } catch (error) {
        console.error('Error loading playlists config:', error);
        throw error;
    }
}

// Populate the playlist dropdown
function populatePlaylistDropdown() {
    playlistDropdown.innerHTML = '';

    availablePlaylists.forEach(playlist => {
        const option = document.createElement('option');
        option.value = playlist.id;
        option.textContent = playlist.title;
        playlistDropdown.appendChild(option);
    });
}

// Load a specific playlist
async function loadPlaylist(playlistId) {
    try {
        const playlistConfig = availablePlaylists.find(p => p.id === playlistId);
        if (!playlistConfig) {
            throw new Error(`Playlist ${playlistId} not found`);
        }

        // Fetch playlist data
        const response = await fetch(`data/${playlistConfig.filename}`);
        const data = await response.json();

        // Update current playlist
        currentPlaylistId = playlistId;
        playlist = data.songs;
        currentTrackIndex = 0;

        // Update UI
        playlistTitle.textContent = data.playlistTitle;
        playlistDescription.textContent = data.playlistDescription;
        playlistDropdown.value = playlistId;

        // Render playlist
        renderPlaylist();

        // Reset player state
        resetPlayerState();

    } catch (error) {
        console.error('Error loading playlist:', error);
        playlistTitle.textContent = 'Error loading playlist';
        playlistDescription.textContent = 'Please try again later';
    }
}

// Reset player state when switching playlists
function resetPlayerState() {
    // Stop current playback
    if (isPlaying) {
        pauseTrack();
    }

    // Reset audio player
    audioPlayer.src = '';

    // Reset track info
    trackTitle.textContent = 'Select a track';
    trackArtist.textContent = '';
    trackAlbum.textContent = '';
    trackYear.textContent = '';
    trackGenre.textContent = '';

    // Reset album art
    albumArt.src = 'assets/default-album-art.svg';

    // Reset progress
    progress.style.width = '0%';
    currentTimeDisplay.textContent = '0:00';
    durationDisplay.textContent = '0:00';

    // Clear search
    searchInput.value = '';

    // Close lyrics if open
    if (!lyricsContainer.classList.contains('hidden')) {
        lyricsContainer.classList.add('hidden');
    }
}

// Render the playlist tracks
function renderPlaylist(filteredPlaylist = null) {
    // Clear the playlist element
    playlistElement.innerHTML = '';
    
    // Use either the filtered playlist or the full playlist
    const tracksToRender = filteredPlaylist || playlist;
    
    // Create and append playlist items
    tracksToRender.forEach((track, index) => {
        const listItem = document.createElement('li');
        listItem.className = 'playlist-track';
        if (index === currentTrackIndex && !filteredPlaylist) {
            listItem.classList.add('active');
        }
        
        listItem.innerHTML = `
            <div class="track-number">${index + 1}</div>
            <div class="track-details">
                <div class="track-title">${track.title}</div>
                <div class="track-artist">${track.artist}</div>
            </div>
            <div class="track-duration">${track.duration}</div>
        `;
        
        // Add click event to play the track
        listItem.addEventListener('click', () => {
            if (filteredPlaylist) {
                // Find the index in the original playlist
                const originalIndex = playlist.findIndex(song => song.id === track.id);
                if (originalIndex !== -1) {
                    loadTrack(originalIndex);
                    playTrack();
                }
            } else {
                loadTrack(index);
                playTrack();
            }
        });
        
        playlistElement.appendChild(listItem);
    });
    
    // If no tracks found
    if (tracksToRender.length === 0) {
        const noResults = document.createElement('li');
        noResults.className = 'no-results';
        noResults.textContent = 'No tracks found';
        playlistElement.appendChild(noResults);
    }
}

// Set up event listeners
function setupEventListeners() {
    // Play/Pause button
    playPauseButton.addEventListener('click', togglePlayPause);
    
    // Previous button
    prevButton.addEventListener('click', playPreviousTrack);
    
    // Next button
    nextButton.addEventListener('click', playNextTrack);
    
    // Volume button
    volumeButton.addEventListener('click', toggleMute);
    
    // Volume slider
    volumeSlider.addEventListener('input', handleVolumeChange);
    
    // Lyrics button
    lyricsButton.addEventListener('click', toggleLyrics);
    
    // Close lyrics button
    closeLyricsButton.addEventListener('click', toggleLyrics);
    
    // Progress bar
    progressBar.addEventListener('click', seekTrack);
    
    // Audio player events
    audioPlayer.addEventListener('timeupdate', updateProgress);
    audioPlayer.addEventListener('ended', handleTrackEnd);
    audioPlayer.addEventListener('loadedmetadata', updateDuration);
    
    // Search functionality
    searchButton.addEventListener('click', handleSearch);
    searchInput.addEventListener('keyup', (e) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });

    // Playlist dropdown
    playlistDropdown.addEventListener('change', handlePlaylistChange);
}

// Handle playlist change
async function handlePlaylistChange() {
    const selectedPlaylistId = playlistDropdown.value;
    if (selectedPlaylistId && selectedPlaylistId !== currentPlaylistId) {
        await loadPlaylist(selectedPlaylistId);
    }
}

// Load a track
function loadTrack(index) {
    // Update current track index
    currentTrackIndex = index;
    
    // Get the track data
    const track = playlist[currentTrackIndex];
    
    // Update audio source
    audioPlayer.src = track.mp3Path;
    audioPlayer.load();
    
    // Update track info
    trackTitle.textContent = track.title;
    trackArtist.textContent = track.artist;
    trackAlbum.textContent = track.album;
    trackYear.textContent = track.year;
    trackGenre.textContent = track.genre;
    
    // Update album art
    if (track.thumbnailPath) {
        albumArt.src = track.thumbnailPath;
    } else {
        albumArt.src = 'assets/default-album-art.svg';
    }
    
    // Update playlist highlight
    updateActiveTrack();
    
    // Reset progress
    progress.style.width = '0%';
    currentTimeDisplay.textContent = '0:00';
    
    // Close lyrics if open
    if (!lyricsContainer.classList.contains('hidden')) {
        lyricsContainer.classList.add('hidden');
    }
}

// Play the current track
function playTrack() {
    audioPlayer.play()
        .then(() => {
            isPlaying = true;
            playPauseButton.innerHTML = '<i class="fas fa-pause"></i>';
            albumArtContainer.classList.add('playing');
        })
        .catch(error => {
            console.error('Error playing track:', error);
        });
}

// Pause the current track
function pauseTrack() {
    audioPlayer.pause();
    isPlaying = false;
    playPauseButton.innerHTML = '<i class="fas fa-play"></i>';
    albumArtContainer.classList.remove('playing');
}

// Toggle play/pause
function togglePlayPause() {
    if (audioPlayer.src) {
        if (isPlaying) {
            pauseTrack();
        } else {
            playTrack();
        }
    } else if (playlist.length > 0) {
        loadTrack(0);
        playTrack();
    }
}

// Play the previous track
function playPreviousTrack() {
    let newIndex = currentTrackIndex - 1;
    if (newIndex < 0) {
        newIndex = playlist.length - 1;
    }
    loadTrack(newIndex);
    playTrack();
}

// Play the next track
function playNextTrack() {
    let newIndex = currentTrackIndex + 1;
    if (newIndex >= playlist.length) {
        newIndex = 0;
    }
    loadTrack(newIndex);
    playTrack();
}

// Toggle mute
function toggleMute() {
    if (isMuted) {
        audioPlayer.volume = previousVolume;
        volumeSlider.value = previousVolume;
        volumeButton.innerHTML = '<i class="fas fa-volume-up"></i>';
        isMuted = false;
    } else {
        previousVolume = audioPlayer.volume;
        audioPlayer.volume = 0;
        volumeSlider.value = 0;
        volumeButton.innerHTML = '<i class="fas fa-volume-mute"></i>';
        isMuted = true;
    }
}

// Handle volume change
function handleVolumeChange() {
    const volume = volumeSlider.value;
    audioPlayer.volume = volume;
    
    // Update volume icon
    if (volume === 0) {
        volumeButton.innerHTML = '<i class="fas fa-volume-mute"></i>';
        isMuted = true;
    } else {
        volumeButton.innerHTML = '<i class="fas fa-volume-up"></i>';
        isMuted = false;
    }
}

// Toggle lyrics display
function toggleLyrics() {
    lyricsContainer.classList.toggle('hidden');
    
    // If showing lyrics, load them
    if (!lyricsContainer.classList.contains('hidden')) {
        loadLyrics();
    }
}

// Load lyrics for the current track
async function loadLyrics() {
    const track = playlist[currentTrackIndex];
    
    // Clear previous lyrics
    lyricsContent.innerHTML = '';
    
    if (track.lyricsPath) {
        try {
            const response = await fetch(track.lyricsPath);
            const text = await response.text();
            
            // Convert markdown to HTML (simple version)
            const html = convertMarkdownToHtml(text);
            lyricsContent.innerHTML = html;
        } catch (error) {
            console.error('Error loading lyrics:', error);
            lyricsContent.innerHTML = '<p>Lyrics not available</p>';
        }
    } else {
        lyricsContent.innerHTML = '<p>Lyrics not available for this track</p>';
    }
}

// Simple markdown to HTML converter
function convertMarkdownToHtml(markdown) {
    // This is a very basic implementation
    let html = markdown
        // Headers
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        // Bold
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // Italic
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // Line breaks
        .replace(/\n/g, '<br>');
    
    return html;
}

// Update progress bar
function updateProgress() {
    const currentTime = audioPlayer.currentTime;
    const duration = audioPlayer.duration;
    
    if (duration) {
        // Update progress bar width
        const progressPercent = (currentTime / duration) * 100;
        progress.style.width = `${progressPercent}%`;
        
        // Update current time display
        currentTimeDisplay.textContent = formatTime(currentTime);
    }
}

// Update duration display
function updateDuration() {
    const duration = audioPlayer.duration;
    durationDisplay.textContent = formatTime(duration);
}

// Format time in MM:SS
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
}

// Seek to a position in the track
function seekTrack(e) {
    const width = progressBar.clientWidth;
    const clickX = e.offsetX;
    const duration = audioPlayer.duration;
    
    audioPlayer.currentTime = (clickX / width) * duration;
}

// Handle track end
function handleTrackEnd() {
    playNextTrack();
}

// Update active track in playlist
function updateActiveTrack() {
    const playlistTracks = document.querySelectorAll('.playlist-track');
    
    playlistTracks.forEach((track, index) => {
        if (index === currentTrackIndex) {
            track.classList.add('active');
        } else {
            track.classList.remove('active');
        }
    });
}

// Handle search
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase().trim();
    
    if (searchTerm === '') {
        // If search is empty, show all tracks
        renderPlaylist();
        return;
    }
    
    // Filter tracks based on search term
    const filteredTracks = playlist.filter(track => 
        track.title.toLowerCase().includes(searchTerm) || 
        track.artist.toLowerCase().includes(searchTerm) ||
        track.album.toLowerCase().includes(searchTerm) ||
        track.genre.toLowerCase().includes(searchTerm)
    );
    
    // Render filtered playlist
    renderPlaylist(filteredTracks);
}

// Initialize the player when the page loads
window.addEventListener('DOMContentLoaded', initPlayer);
