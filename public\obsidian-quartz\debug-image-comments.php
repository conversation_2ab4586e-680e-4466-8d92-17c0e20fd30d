<?php
/**
 * Debug script for image comments
 */

// Load configuration
$config = require_once 'config.php';
require_once 'path-helper.php';
$paths = initPaths($config, __FILE__);

$page_title = 'Debug Image Comments';
$meta_description = 'Debug image comments functionality';

ob_start();
?>

<div style="max-width: 800px; margin: 2rem auto; padding: 1rem; font-family: Arial, sans-serif;">
    <h1>Debug Image Comments</h1>
    
    <div style="background: #f5f5f5; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
        <h3>Test Comment Submission</h3>
        <form id="debug-form" style="display: flex; flex-direction: column; gap: 1rem;">
            <input type="text" id="debug-name" placeholder="Your Name" required>
            <input type="email" id="debug-email" placeholder="Your Email" required>
            <textarea id="debug-content" placeholder="Your Comment" rows="3" required></textarea>
            <button type="submit">Submit Test Comment</button>
        </form>
        <div id="debug-result" style="margin-top: 1rem; padding: 1rem; background: white; border-radius: 4px; display: none;"></div>
    </div>
    
    <div style="background: #f5f5f5; padding: 1rem; border-radius: 8px; margin-bottom: 2rem;">
        <h3>Load Comments Test</h3>
        <button onclick="loadTestComments()">Load Comments for Test Image</button>
        <div id="comments-result" style="margin-top: 1rem; padding: 1rem; background: white; border-radius: 4px; display: none;"></div>
    </div>
    
    <div style="background: #f5f5f5; padding: 1rem; border-radius: 8px;">
        <h3>Console Output</h3>
        <p>Check the browser console (F12) for detailed debug information.</p>
        <div id="console-output" style="background: #000; color: #0f0; padding: 1rem; font-family: monospace; height: 200px; overflow-y: auto;"></div>
    </div>
</div>

<script>
const testImageSlug = 'image-self-30degrees-png';
const handlerUrl = 'comments/comment-handler.php';

// Override console.log to also display in our debug area
const originalLog = console.log;
const consoleOutput = document.getElementById('console-output');

console.log = function(...args) {
    originalLog.apply(console, args);
    const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');
    consoleOutput.innerHTML += message + '\n';
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
};

document.getElementById('debug-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const name = document.getElementById('debug-name').value;
    const email = document.getElementById('debug-email').value;
    const content = document.getElementById('debug-content').value;
    
    console.log('=== SUBMITTING TEST COMMENT ===');
    console.log('Image slug:', testImageSlug);
    console.log('Name:', name);
    console.log('Email:', email);
    console.log('Content:', content);
    
    const resultDiv = document.getElementById('debug-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = 'Submitting...';
    
    try {
        const response = await fetch(handlerUrl + '?action=comment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                post_slug: testImageSlug,
                name: name,
                email: email,
                content: content,
                parent_id: null
            })
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        let result;
        try {
            result = JSON.parse(responseText);
            console.log('Parsed result:', result);
        } catch (e) {
            console.error('JSON parse error:', e);
            resultDiv.innerHTML = `<strong>Error:</strong> Invalid JSON response<br><pre>${responseText}</pre>`;
            return;
        }
        
        if (result.success) {
            resultDiv.innerHTML = `<strong>Success!</strong> Comment posted with ID: ${result.comment_id || 'unknown'}`;
            document.getElementById('debug-form').reset();
        } else {
            resultDiv.innerHTML = `<strong>Error:</strong> ${result.error || 'Unknown error'}`;
        }
        
    } catch (error) {
        console.error('Fetch error:', error);
        resultDiv.innerHTML = `<strong>Network Error:</strong> ${error.message}`;
    }
});

async function loadTestComments() {
    console.log('=== LOADING TEST COMMENTS ===');
    console.log('Image slug:', testImageSlug);
    
    const resultDiv = document.getElementById('comments-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = 'Loading...';
    
    try {
        const url = `${handlerUrl}?action=comments&post_slug=${encodeURIComponent(testImageSlug)}&page=1`;
        console.log('Fetching from:', url);
        
        const response = await fetch(url);
        console.log('Response status:', response.status);
        
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        let result;
        try {
            result = JSON.parse(responseText);
            console.log('Parsed result:', result);
        } catch (e) {
            console.error('JSON parse error:', e);
            resultDiv.innerHTML = `<strong>Error:</strong> Invalid JSON response<br><pre>${responseText}</pre>`;
            return;
        }
        
        if (result.success) {
            const comments = result.comments || [];
            const total = result.total_comments || 0;
            
            let html = `<strong>Success!</strong> Found ${total} comments:<br><br>`;
            
            if (comments.length === 0) {
                html += '<em>No comments found</em>';
            } else {
                comments.forEach(comment => {
                    html += `<div style="border: 1px solid #ddd; padding: 0.5rem; margin: 0.5rem 0;">
                        <strong>${comment.name}</strong> (${comment.created_at})<br>
                        ${comment.content}
                    </div>`;
                });
            }
            
            resultDiv.innerHTML = html;
        } else {
            resultDiv.innerHTML = `<strong>Error:</strong> ${result.error || 'Unknown error'}`;
        }
        
    } catch (error) {
        console.error('Fetch error:', error);
        resultDiv.innerHTML = `<strong>Network Error:</strong> ${error.message}`;
    }
}

console.log('Debug page loaded. Test image slug:', testImageSlug);
</script>

<?php
$content = ob_get_clean();
include 'page template.htm';
?>
