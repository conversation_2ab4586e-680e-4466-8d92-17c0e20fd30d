<?php
// Music Player Page - Integrated with site template
require_once '../../config.php';

// Page-specific variables
$page_title = 'Music Playlists';
$meta_description = 'Listen to curated music playlists including mixed favorites, rock classics, chill vibes, and electronic beats.';
$meta_keywords = 'music, playlists, songs, audio player, rock, electronic, chill, favorites';

// Additional CSS and JS for music player
$additional_css = '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="../../css/music-player.css?v=' . time() . '">';

$additional_js = '<script src="js/player.js"></script>';

// Content for the music player
$content = '
<div class="music-player-container">
    <div class="playlist-header">
        <div class="playlist-selector-wrapper">
            <label for="playlist-dropdown">Choose Playlist:</label>
            <select id="playlist-dropdown" class="playlist-dropdown">
                <option value="">Loading playlists...</option>
            </select>
        </div>
        <div class="playlist-info">
            <h1 id="playlist-title">Loading playlist...</h1>
            <p id="playlist-description"></p>
        </div>
    </div>

    <div class="player-main">
        <div class="now-playing-section">
            <div class="album-art-container">
                <img id="album-art" src="assets/default-album-art.svg" alt="Album Art">
                <div class="vinyl-record"></div>
            </div>
            <div class="track-info">
                <h2 id="track-title">Select a track</h2>
                <h3 id="track-artist"></h3>
                <p id="track-album"></p>
                <div class="track-meta">
                    <span id="track-year"></span>
                    <span id="track-genre"></span>
                </div>
            </div>
        </div>
        
        <div class="player-controls">
            <div class="progress-container">
                <span id="current-time">0:00</span>
                <div class="progress-bar">
                    <div class="progress"></div>
                </div>
                <span id="duration">0:00</span>
            </div>
            
            <div class="control-buttons">
                <button id="prev-button" class="control-btn">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button id="play-pause-button" class="control-btn play-pause">
                    <i class="fas fa-play"></i>
                </button>
                <button id="next-button" class="control-btn">
                    <i class="fas fa-step-forward"></i>
                </button>
                <button id="lyrics-button" class="control-btn">
                    <i class="fas fa-file-text"></i>
                </button>
            </div>
            
            <div class="volume-controls">
                <button id="volume-button" class="control-btn">
                    <i class="fas fa-volume-up"></i>
                </button>
                <input type="range" id="volume-slider" class="volume-slider" min="0" max="1" step="0.1" value="1">
            </div>
        </div>
    </div>

    <div class="playlist-section">
        <div class="search-container">
            <input type="text" id="search-input" placeholder="Search songs...">
            <button id="search-button"><i class="fas fa-search"></i></button>
        </div>
        <div class="playlist-wrapper">
            <h3>Playlist</h3>
            <ul id="playlist" class="playlist-tracks">
                <!-- Tracks will be populated here by JavaScript -->
            </ul>
        </div>
    </div>

    <div id="lyrics-container" class="lyrics-container hidden">
        <div class="lyrics-header">
            <h2>Lyrics & Commentary</h2>
            <button id="close-lyrics-button"><i class="fas fa-times"></i></button>
        </div>
        <div class="lyrics-content-wrapper">
            <div id="lyrics-content" class="lyrics-content">
                <!-- Lyrics will be populated here by JavaScript -->
            </div>
            <div id="commentary-content" class="commentary-content">
                <!-- Commentary will be populated here by JavaScript -->
            </div>
        </div>
    </div>
</div>

<audio id="audio-player"></audio>
';

// Include the template
include '../../page template.htm';
